/**
 * GDPR Database Service
 *
 * Handles all database operations for GDPR compliance scans.
 * Follows the same pattern as HIPAA database service for consistency.
 * NO MOCK DATA - stores real scan results only
 */

import db from '../../../lib/db';
import { v4 as uuidv4 } from 'uuid';
import { Knex } from 'knex';
import {
  GdprScanResult,
  GdprScanEntity,
  GdprCheckResultEntity,
  GdprCheckResult,
  GdprScanMetadata,
  RiskLevel,
  ScanStatus,
  GdprCategory,
  CategoryBreakdown,
  Evidence,
  Recommendation,
} from '../types';
import { GDPR_SCORING_WEIGHTS } from '../constants';

export interface GdprScanConfig {
  userId: string;
  targetUrl: string;
  scanOptions: Record<string, unknown>;
  scanId?: string; // Optional: if provided, use this ID instead of generating one
}

export interface GdprScanUpdateData {
  overallScore: number;
  riskLevel: RiskLevel;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  scanDuration: number;
  status: ScanStatus;
}

export class GdprDatabase {
  /**
   * Safe JSON stringify helper to prevent database JSON errors
   */
  private static stringifyJsonSafely(data: unknown): string {
    if (data === null || data === undefined) {
      return JSON.stringify([]);
    }
    try {
      // Ensure the data is properly serializable
      const serialized = JSON.stringify(data);
      // Validate it can be parsed back
      JSON.parse(serialized);
      return serialized;
    } catch (error) {
      console.warn('Failed to stringify JSON safely:', data, error);
      return JSON.stringify([]);
    }
  }

  /**
   * Safe JSON parse helper to handle database JSON fields
   */
  private static parseJsonSafely<T = unknown>(data: unknown): T[] {
    if (data === null || data === undefined) {
      return [];
    }
    try {
      if (typeof data === 'string') {
        const parsed = JSON.parse(data);
        return Array.isArray(parsed) ? parsed : [];
      }
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.warn('Failed to parse JSON safely:', data, error);
      return [];
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔌 Testing GDPR database connection...');

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Database connection timeout after 10 seconds')), 10000);
      });

      const testPromise = db.raw('SELECT 1 as test');

      await Promise.race([testPromise, timeoutPromise]);

      console.log('✅ GDPR database connection successful');
      return true;
    } catch (error) {
      console.error('❌ GDPR database connection failed:', error);
      return false;
    }
  }

  /**
   * Check if GDPR tables exist
   */
  async checkTableExists(): Promise<boolean> {
    try {
      const tableExists = await db.schema.hasTable('gdpr_scans');
      console.log(`📋 GDPR tables exist: ${tableExists}`);
      return tableExists;
    } catch (error) {
      console.error('❌ Error checking GDPR table existence:', error);
      return false;
    }
  }

  /**
   * Create a new GDPR scan record
   */
  static async createScan(config: GdprScanConfig): Promise<string> {
    try {
      console.log('📝 Creating GDPR scan record...');

      const insertData: any = {
        user_id: config.userId,
        target_url: config.targetUrl,
        scan_timestamp: new Date(),
        total_checks: 0,
        passed_checks: 0,
        failed_checks: 0,
        manual_review_required: 0,
        scan_status: 'pending',
        metadata: config.scanOptions,
      };

      // Use provided scanId if available, otherwise let database generate one
      if (config.scanId) {
        insertData.id = config.scanId;
      }

      const [scan] = await db('gdpr_scans')
        .insert(insertData)
        .returning('id');

      const scanId = typeof scan === 'object' ? scan.id : scan;
      console.log(`✅ GDPR scan record created with ID: ${scanId}`);
      return scanId;
    } catch (error) {
      console.error('❌ Error creating GDPR scan record:', error);
      throw error;
    }
  }

  /**
   * Update scan status
   */
  async updateScanStatus(scanId: string, status: ScanStatus, errorMessage?: string): Promise<void> {
    try {
      console.log(`📝 Updating GDPR scan ${scanId} status to: ${status}`);

      const updateData: Partial<GdprScanEntity> = {
        scan_status: status,
        updated_at: new Date(),
      };

      if (errorMessage) {
        updateData.error_message = errorMessage;
      }

      await db('gdpr_scans').where('id', scanId).update(updateData);

      console.log(`✅ GDPR scan status updated successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan status:', error);
      throw error;
    }
  }

  /**
   * Update scan with final results
   */
  async updateScanResults(scanId: string, data: GdprScanUpdateData): Promise<void> {
    try {
      console.log(`📝 Updating GDPR scan ${scanId} with final results...`);

      await db('gdpr_scans').where('id', scanId).update({
        scan_duration: data.scanDuration,
        overall_score: data.overallScore,
        risk_level: data.riskLevel,
        total_checks: data.totalChecks,
        passed_checks: data.passedChecks,
        failed_checks: data.failedChecks,
        manual_review_required: data.manualReviewRequired,
        scan_status: data.status,
        updated_at: new Date(),
      });

      console.log(`✅ GDPR scan results updated successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan results:', error);
      throw error;
    }
  }

  /**
   * Update existing scan with final results and save check results
   */
  static async updateScanWithResults(scanId: string, scanResult: GdprScanResult): Promise<void> {
    try {
      console.log(`📝 Updating existing scan ${scanId} with final results...`);

      await db.transaction(async (trx: Knex.Transaction) => {
        // Update main scan record with final results
        await trx('gdpr_scans').where('id', scanId).update({
          scan_duration: scanResult.scanDuration,
          overall_score: scanResult.overallScore,
          risk_level: scanResult.riskLevel,
          total_checks: scanResult.summary.totalChecks,
          passed_checks: scanResult.summary.passedChecks,
          failed_checks: scanResult.summary.failedChecks,
          manual_review_required: scanResult.summary.manualReviewRequired,
          scan_status: scanResult.status,
          updated_at: new Date(),
        });

        // Insert check results with safe JSON serialization
        const checkResults = scanResult.checks.map((check) => ({
          id: uuidv4(),
          scan_id: scanId,
          rule_id: check.ruleId,
          rule_name: check.ruleName,
          category: check.category,
          passed: check.passed,
          score: check.score,
          weight: check.weight,
          severity: check.severity,
          manual_review_required: check.manualReviewRequired,
          evidence: GdprDatabase.stringifyJsonSafely(check.evidence),
          recommendations: GdprDatabase.stringifyJsonSafely(check.recommendations),
        }));

        await trx('gdpr_check_results').insert(checkResults);
      });

      console.log(`✅ GDPR scan ${scanId} updated with final results successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan with results:', error);
      throw error;
    }
  }

  /**
   * Store individual check results
   */
  async storeCheckResults(scanId: string, checkResults: GdprCheckResult[]): Promise<void> {
    try {
      console.log(`📝 Storing ${checkResults.length} GDPR check results...`);

      const checkEntities = checkResults.map((result) => ({
        scan_id: scanId,
        rule_id: result.ruleId,
        rule_name: result.ruleName,
        category: result.category,
        passed: result.passed,
        score: result.score,
        weight: result.weight,
        severity: result.severity,
        manual_review_required: result.manualReviewRequired,
        evidence: GdprDatabase.stringifyJsonSafely(result.evidence),
        recommendations: GdprDatabase.stringifyJsonSafely(result.recommendations),
        created_at: new Date(),
      }));

      await db('gdpr_check_results').insert(checkEntities);

      console.log(`✅ GDPR check results stored successfully`);
    } catch (error) {
      console.error('❌ Error storing GDPR check results:', error);
      throw error;
    }
  }

  /**
   * Get scan by ID
   */
  async getScanById(scanId: string): Promise<GdprScanEntity | null> {
    try {
      const scan = await db('gdpr_scans').where('id', scanId).first();

      return scan || null;
    } catch (error) {
      console.error('❌ Error fetching GDPR scan:', error);
      throw error;
    }
  }

  /**
   * Get check results for a scan
   */
  static async getCheckResults(scanId: string): Promise<GdprCheckResultEntity[]> {
    try {
      const results = await db('gdpr_check_results')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc');

      return results;
    } catch (error) {
      console.error('❌ Error fetching GDPR check results:', error);
      throw error;
    }
  }

  /**
   * Get scans for a user
   */
  async getUserScans(userId: string, limit = 10): Promise<GdprScanEntity[]> {
    try {
      const scans = await db('gdpr_scans')
        .where('user_id', userId)
        .orderBy('scan_timestamp', 'desc')
        .limit(limit);

      return scans;
    } catch (error) {
      console.error('❌ Error fetching user GDPR scans:', error);
      throw error;
    }
  }

  /**
   * Save complete GDPR scan result to database
   * NO MOCK DATA - stores real scan results only
   */
  static async saveScanResult(userId: string, scanResult: GdprScanResult): Promise<string> {
    const scanId = uuidv4();

    try {
      await db.transaction(async (trx: Knex.Transaction) => {
        // Insert main scan record
        await trx('gdpr_scans').insert({
          id: scanId,
          user_id: userId,
          target_url: scanResult.targetUrl,
          scan_timestamp: new Date(scanResult.timestamp),
          scan_duration: scanResult.scanDuration,
          overall_score: scanResult.overallScore,
          risk_level: scanResult.riskLevel,
          total_checks: scanResult.summary.totalChecks,
          passed_checks: scanResult.summary.passedChecks,
          failed_checks: scanResult.summary.failedChecks,
          manual_review_required: scanResult.summary.manualReviewRequired,
          scan_status: scanResult.status,
          metadata: scanResult.metadata,
        });

        // Insert check results with safe JSON serialization
        const checkResults = scanResult.checks.map((check) => ({
          id: uuidv4(),
          scan_id: scanId,
          rule_id: check.ruleId,
          rule_name: check.ruleName,
          category: check.category,
          passed: check.passed,
          score: check.score,
          weight: check.weight,
          severity: check.severity,
          manual_review_required: check.manualReviewRequired,
          evidence: GdprDatabase.stringifyJsonSafely(check.evidence),
          recommendations: GdprDatabase.stringifyJsonSafely(check.recommendations),
        }));

        await trx('gdpr_check_results').insert(checkResults);
      });

      console.log(`✅ GDPR scan results saved to database: ${scanId}`);
      return scanId;
    } catch (error) {
      console.error('❌ Failed to save GDPR scan results:', error);
      throw new Error(
        `Database save failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Retrieve GDPR scan result by ID
   */
  static async getScanResult(scanId: string): Promise<GdprScanResult | null> {
    try {
      const scanRecord = (await db('gdpr_scans').where('id', scanId).first()) as
        | GdprScanEntity
        | undefined;

      if (!scanRecord) {
        return null;
      }

      const checkResults = (await db('gdpr_check_results')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc')) as GdprCheckResultEntity[];

      return this.mapEntityToResult(scanRecord, checkResults);
    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      throw new Error(
        `Database retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update scan status
   */
  static async updateScanStatus(
    scanId: string,
    status: ScanStatus,
    errorMessage?: string,
  ): Promise<void> {
    try {
      await db('gdpr_scans').where('id', scanId).update({
        scan_status: status,
        error_message: errorMessage,
        updated_at: new Date(),
      });
    } catch (error) {
      console.error('❌ Failed to update scan status:', error);
      throw new Error(
        `Status update failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update manual review status for a specific check
   */
  static async updateManualReview(
    scanId: string,
    ruleId: string,
    reviewData: {
      assessment: string;
      notes: string;
      reviewerName: string;
      reviewedAt: string;
      reviewedBy: string;
    }
  ): Promise<boolean> {
    try {
      console.log(`🔥 ===== DATABASE UPDATE MANUAL REVIEW =====`);
      console.log(`📝 Updating manual review for scan ${scanId}, rule ${ruleId}`);
      console.log(`📝 Review data:`, JSON.stringify(reviewData, null, 2));

      // First, check if the record exists
      const existingRecord = await db('gdpr_check_results')
        .where({ scan_id: scanId, rule_id: ruleId })
        .first();

      console.log(`🔍 Existing record check:`, existingRecord ? 'FOUND' : 'NOT FOUND');
      if (existingRecord) {
        console.log(`🔍 Existing record details:`, JSON.stringify(existingRecord, null, 2));
      }

      // Update the check result with manual review data
      console.log(`💾 Executing database update...`);
      const updateResult = await db('gdpr_check_results')
        .where({ scan_id: scanId, rule_id: ruleId })
        .update({
          manual_review_completed: true,
          manual_review_assessment: reviewData.assessment,
          manual_review_notes: reviewData.notes,
          manual_reviewer_name: reviewData.reviewerName,
          manual_reviewed_at: new Date(reviewData.reviewedAt),
          manual_reviewed_by: reviewData.reviewedBy,
          updated_at: new Date(),
        });

      console.log(`💾 Database update result: ${updateResult} rows affected`);

      if (updateResult === 0) {
        console.warn(`⚠️ No check result found for scan ${scanId}, rule ${ruleId}`);

        // List all available records for this scan for debugging
        const allRecords = await db('gdpr_check_results')
          .where({ scan_id: scanId })
          .select('rule_id', 'rule_name');

        console.log(`🔍 Available rules for scan ${scanId}:`, allRecords);
        return false;
      }

      console.log(`✅ Manual review updated successfully for scan ${scanId}, rule ${ruleId}`);
      return true;

    } catch (error) {
      console.error('❌ Error updating manual review:', error);
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  /**
   * Recalculate overall score after manual review updates
   */
  static async recalculateScore(scanId: string): Promise<GdprScanResult | null> {
    try {
      console.log(`🔄 Recalculating score for scan ${scanId}`);

      // Get all check results for the scan
      const checkEntities = await db('gdpr_check_results')
        .where({ scan_id: scanId })
        .select('*');

      if (checkEntities.length === 0) {
        console.warn(`⚠️ No check results found for scan ${scanId}`);
        return null;
      }

      // Map to check results format
      const checks = checkEntities.map((check) => ({
        ruleId: check.rule_id,
        ruleName: check.rule_name,
        category: check.category,
        passed: check.passed,
        score: check.score,
        weight: check.weight,
        severity: check.severity,
        evidence: GdprDatabase.parseJsonSafely(check.evidence),
        recommendations: GdprDatabase.parseJsonSafely(check.recommendations),
        manualReviewRequired: check.manual_review_required,
        manualReviewCompleted: check.manual_review_completed || false,
        manualReviewAssessment: check.manual_review_assessment,
      }));

      // Recalculate score using dual scoring system
      const { overallScore, automatedScore, manualScore, riskLevel } = this.calculateUpdatedScore(checks);

      // Update the scan record
      await db('gdpr_scans')
        .where({ id: scanId })
        .update({
          overall_score: overallScore,
          risk_level: riskLevel,
          updated_at: new Date(),
        });

      console.log(`✅ Score recalculated for scan ${scanId}: Overall=${overallScore}%, Automated=${automatedScore}%, Manual=${manualScore}%`);

      // Return updated scan result
      return await this.getScanResult(scanId);

    } catch (error) {
      console.error('❌ Error recalculating score:', error);
      throw error;
    }
  }

  /**
   * Calculate updated score with dual scoring system (automated 80% + manual 20%)
   */
  private static calculateUpdatedScore(checks: any[]): {
    overallScore: number;
    riskLevel: RiskLevel;
    automatedScore: number;
    manualScore: number;
  } {
    // Use the same weights as defined in constants
    const weights = GDPR_SCORING_WEIGHTS;

    // Manual review rules (7 total) - based on automated: false in constants
    const manualReviewRules = ['GDPR-003', 'GDPR-010', 'GDPR-013', 'GDPR-014', 'GDPR-015', 'GDPR-018', 'GDPR-020'];

    // Separate automated and manual checks
    const automatedChecks = checks.filter(check => !manualReviewRules.includes(check.ruleId));
    const manualChecks = checks.filter(check => manualReviewRules.includes(check.ruleId));

    console.log(`📊 Calculating dual scores: ${automatedChecks.length} automated, ${manualChecks.length} manual checks`);
    console.log(`📊 Manual review rules:`, manualReviewRules);
    console.log(`📊 Manual checks found:`, manualChecks.map(c => ({ ruleId: c.ruleId, completed: c.manualReviewCompleted, assessment: c.manualReviewAssessment })));

    // Calculate Automated Score (14 checks)
    let automatedWeightedScore = 0;
    let automatedTotalWeight = 0;
    let automatedPassedChecks = 0;
    let automatedCriticalFailures = 0;

    for (const check of automatedChecks) {
      const weight = weights[check.ruleId] || 1;
      let score = 0;

      if (check.passed) {
        score = 100;
        automatedPassedChecks++;
      } else {
        score = 0;
        if (weight >= 7) automatedCriticalFailures++;
      }

      automatedWeightedScore += score * weight;
      automatedTotalWeight += weight;
    }

    const automatedBaseScore = automatedTotalWeight > 0 ? automatedWeightedScore / automatedTotalWeight : 0;
    const automatedPenalty = automatedCriticalFailures * 5; // Reduced penalty for automated only
    const automatedScore = Math.max(0, automatedBaseScore - automatedPenalty);

    // Calculate Manual Score (7 checks)
    let manualWeightedScore = 0;
    let manualTotalWeight = 0;
    let manualPassedChecks = 0;
    let manualCompletedChecks = 0;

    for (const check of manualChecks) {
      const weight = weights[check.ruleId] || 1;
      let score = 0;

      console.log(`📊 Manual check ${check.ruleId}: completed=${check.manualReviewCompleted}, assessment=${check.manualReviewAssessment}`);

      if (check.manualReviewCompleted) {
        manualCompletedChecks++;
        switch (check.manualReviewAssessment) {
          case 'compliant':
            score = 100;
            manualPassedChecks++;
            break;
          case 'partially-compliant':
            score = 60;
            break;
          case 'non-compliant':
            score = 0;
            break;
          case 'needs-review':
            score = 30;
            break;
          default:
            score = 0;
        }
        console.log(`📊 Manual check ${check.ruleId}: score=${score}, weight=${weight}, contribution=${score * weight}`);
      } else {
        // Pending manual review - no score contribution
        score = 0;
        console.log(`📊 Manual check ${check.ruleId}: pending review, no score contribution`);
      }

      manualWeightedScore += score * weight;
      manualTotalWeight += weight;
    }

    console.log(`📊 Manual review totals: completed=${manualCompletedChecks}/${manualChecks.length}, weightedScore=${manualWeightedScore}, totalWeight=${manualTotalWeight}`);

    // Manual score calculation
    let manualScore = 0;
    if (manualCompletedChecks > 0) {
      manualScore = manualTotalWeight > 0 ? manualWeightedScore / manualTotalWeight : 0;
    }

    // Overall Score Calculation: (Automated Score × 0.8) + (Manual Score × 0.2)
    const overallScore = (automatedScore * 0.8) + (manualScore * 0.2);

    console.log(`📊 Score breakdown: Automated=${Math.round(automatedScore)}%, Manual=${Math.round(manualScore)}%, Overall=${Math.round(overallScore)}%`);

    // Determine risk level based on overall score and critical failures
    let riskLevel: RiskLevel;
    const totalCriticalFailures = automatedCriticalFailures + (manualChecks.filter(c =>
      c.manualReviewCompleted && c.manualReviewAssessment === 'non-compliant' && weights[c.ruleId] >= 7
    ).length);

    if (totalCriticalFailures > 0) {
      riskLevel = 'critical';
    } else if (overallScore >= 80) {
      riskLevel = 'low';
    } else if (overallScore >= 60) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'high';
    }

    return {
      overallScore: Math.round(overallScore),
      automatedScore: Math.round(automatedScore),
      manualScore: Math.round(manualScore),
      riskLevel,
    };
  }

  /**
   * Get user's GDPR scan history
   */
  static async getUserScans(
    userId: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<GdprScanEntity[]> {
    try {
      return (await db('gdpr_scans')
        .where('user_id', userId)
        .orderBy('scan_timestamp', 'desc')
        .limit(limit)
        .offset(offset)) as GdprScanEntity[];
    } catch (error) {
      console.error('❌ Failed to retrieve user scans:', error);
      throw new Error(
        `Scan history retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Save cookie analysis results
   */
  static async saveCookieAnalysis(
    scanId: string,
    cookies: Array<{
      name: string;
      domain: string;
      category: string;
      hasConsent: boolean;
      secureFlag: boolean;
      httpOnlyFlag: boolean;
      sameSiteAttribute?: string;
      expiryDate?: Date;
      purpose?: string;
      thirdParty: boolean;
    }>,
  ): Promise<void> {
    try {
      const cookieRecords = cookies.map((cookie) => ({
        id: uuidv4(),
        scan_id: scanId,
        cookie_name: cookie.name,
        cookie_domain: cookie.domain,
        cookie_category: cookie.category,
        has_consent: cookie.hasConsent,
        secure_flag: cookie.secureFlag,
        httponly_flag: cookie.httpOnlyFlag,
        samesite_attribute: cookie.sameSiteAttribute,
        expiry_date: cookie.expiryDate,
        purpose: cookie.purpose,
        third_party: cookie.thirdParty,
      }));

      await db('gdpr_cookie_analysis').insert(cookieRecords);
    } catch (error) {
      console.error('❌ Failed to save cookie analysis:', error);
      throw new Error(
        `Cookie analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Save consent analysis results
   */
  static async saveConsentAnalysis(
    scanId: string,
    consentData: {
      consentType: string;
      consentMechanism: string;
      hasRejectOption: boolean;
      hasGranularOptions: boolean;
      preTickedBoxes: boolean;
      consentText: string;
      privacyPolicyLinked: boolean;
      compliant: boolean;
      issues: string[];
    },
  ): Promise<void> {
    try {
      await db('gdpr_consent_analysis').insert({
        id: uuidv4(),
        scan_id: scanId,
        consent_type: consentData.consentType,
        consent_mechanism: consentData.consentMechanism,
        has_reject_option: consentData.hasRejectOption,
        has_granular_options: consentData.hasGranularOptions,
        pre_ticked_boxes: consentData.preTickedBoxes,
        consent_text: consentData.consentText,
        privacy_policy_linked: consentData.privacyPolicyLinked,
        compliant: consentData.compliant,
        issues: consentData.issues,
      });
    } catch (error) {
      console.error('❌ Failed to save consent analysis:', error);
      throw new Error(
        `Consent analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Save tracker analysis results
   */
  static async saveTrackerAnalysis(
    scanId: string,
    trackers: Array<{
      domain: string;
      type: string;
      name: string;
      loadsBeforeConsent: boolean;
      hasConsentMechanism: boolean;
      dataTransferred: string;
      privacyPolicyMentioned: boolean;
      compliant: boolean;
    }>,
  ): Promise<void> {
    try {
      console.log(`📝 Saving tracker analysis for scan ${scanId}: ${trackers.length} trackers`);

      // Handle empty tracker arrays gracefully
      if (!trackers || trackers.length === 0) {
        console.log(`ℹ️ No trackers found for scan ${scanId}, skipping tracker analysis save`);
        return;
      }

      const trackerRecords = trackers.map((tracker) => ({
        id: uuidv4(),
        scan_id: scanId,
        tracker_domain: tracker.domain,
        tracker_type: tracker.type,
        tracker_name: tracker.name,
        loads_before_consent: tracker.loadsBeforeConsent,
        has_consent_mechanism: tracker.hasConsentMechanism,
        data_transferred: tracker.dataTransferred,
        privacy_policy_mentioned: tracker.privacyPolicyMentioned,
        compliant: tracker.compliant,
      }));

      console.log(`💾 Inserting ${trackerRecords.length} tracker records into database`);
      await db('gdpr_tracker_analysis').insert(trackerRecords);
      console.log(`✅ Successfully saved ${trackerRecords.length} tracker analysis records`);
    } catch (error) {
      console.error('❌ Failed to save tracker analysis:', error);
      console.error('❌ Tracker data:', trackers);
      throw new Error(
        `Tracker analysis save failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Map database entities to result objects
   */
  public static mapEntityToResult(
    scanEntity: GdprScanEntity,
    checkEntities: GdprCheckResultEntity[],
  ): GdprScanResult {
    const checks = checkEntities.map((check) => ({
      ruleId: check.rule_id,
      ruleName: check.rule_name,
      category: check.category,
      passed: check.passed,
      score: check.score,
      weight: check.weight,
      severity: check.severity,
      evidence: GdprDatabase.parseJsonSafely<Evidence>(check.evidence),
      recommendations: GdprDatabase.parseJsonSafely<Recommendation>(check.recommendations),
      manualReviewRequired: check.manual_review_required,
      manualReviewCompleted: (check as any).manual_review_completed || false,
      manualReviewAssessment: (check as any).manual_review_assessment,
      manualReviewNotes: (check as any).manual_review_notes,
      manualReviewerName: (check as any).manual_reviewer_name,
      manualReviewedAt: (check as any).manual_reviewed_at,
      manualReviewedBy: (check as any).manual_reviewed_by,
    }));

    const categoryBreakdown = this.calculateCategoryBreakdown(checks);

    return {
      scanId: scanEntity.id,
      targetUrl: scanEntity.target_url,
      timestamp: scanEntity.scan_timestamp.toISOString(),
      scanDuration: scanEntity.scan_duration,
      overallScore: scanEntity.overall_score,
      riskLevel: scanEntity.risk_level,
      status: scanEntity.scan_status,
      summary: {
        totalChecks: scanEntity.total_checks,
        passedChecks: scanEntity.passed_checks,
        failedChecks: scanEntity.failed_checks,
        manualReviewRequired: scanEntity.manual_review_required,
        criticalFailures: checks.filter((c) => !c.passed && c.severity === 'critical').length,
        categoryBreakdown,
      },
      checks,
      recommendations: [], // Will be populated by orchestrator
      metadata: this.ensureValidMetadata(scanEntity.metadata)
    };
  }

  /**
   * Ensure metadata has valid structure with defaults for missing properties
   */
  private static ensureValidMetadata(metadata: Record<string, unknown> | null | undefined): GdprScanMetadata {
    const defaultScanOptions = {
      enableCookieAnalysis: true,
      enableTrackerDetection: true,
      enableConsentTesting: true,
      maxPages: 10,
      timeout: 300000,
      userAgent: 'GDPR-Scanner/1.0'
    };

    const defaultMetadata: GdprScanMetadata = {
      version: '1.0.0',
      processingTime: 0,
      checksPerformed: 0,
      analysisLevelsUsed: ['Level 1', 'Level 2', 'Level 3'],
      errors: [],
      warnings: [],
      userAgent: 'GDPR-Scanner/1.0',
      scanOptions: defaultScanOptions
    };

    if (!metadata || typeof metadata !== 'object') {
      return defaultMetadata;
    }

    // Safely extract scanOptions with proper typing
    const scanOptions = metadata.scanOptions as Record<string, unknown> | undefined;
    const validScanOptions = {
      enableCookieAnalysis: (scanOptions?.enableCookieAnalysis as boolean) ?? defaultScanOptions.enableCookieAnalysis,
      enableTrackerDetection: (scanOptions?.enableTrackerDetection as boolean) ?? defaultScanOptions.enableTrackerDetection,
      enableConsentTesting: (scanOptions?.enableConsentTesting as boolean) ?? defaultScanOptions.enableConsentTesting,
      maxPages: (scanOptions?.maxPages as number) ?? defaultScanOptions.maxPages,
      timeout: (scanOptions?.timeout as number) ?? defaultScanOptions.timeout,
      userAgent: (scanOptions?.userAgent as string) ?? defaultScanOptions.userAgent
    };

    return {
      version: (metadata.version as string) || defaultMetadata.version,
      processingTime: (metadata.processingTime as number) || defaultMetadata.processingTime,
      checksPerformed: (metadata.checksPerformed as number) || defaultMetadata.checksPerformed,
      analysisLevelsUsed: (metadata.analysisLevelsUsed as string[]) || defaultMetadata.analysisLevelsUsed,
      errors: (metadata.errors as string[]) || defaultMetadata.errors,
      warnings: (metadata.warnings as string[]) || defaultMetadata.warnings,
      userAgent: (metadata.userAgent as string) || defaultMetadata.userAgent,
      scanOptions: validScanOptions
    };
  }

  /**
   * Calculate category breakdown for summary
   */
  private static calculateCategoryBreakdown(checks: GdprCheckResult[]): CategoryBreakdown[] {
    const categoryMap = new Map<
      GdprCategory,
      {
        category: GdprCategory;
        score: number;
        checksInCategory: number;
        passedInCategory: number;
      }
    >();

    for (const check of checks) {
      const existing = categoryMap.get(check.category) || {
        category: check.category,
        score: 0,
        checksInCategory: 0,
        passedInCategory: 0,
      };

      existing.checksInCategory++;
      if (check.passed) {
        existing.passedInCategory++;
      }
      existing.score =
        existing.checksInCategory > 0
          ? Math.round((existing.passedInCategory / existing.checksInCategory) * 100)
          : 0;

      categoryMap.set(check.category, existing);
    }

    return Array.from(categoryMap.values());
  }

  /**
   * Get cookie analysis for a scan
   */
  static async getCookieAnalysis(scanId: string): Promise<
    Array<{
      id: string;
      scan_id: string;
      cookie_name: string;
      cookie_domain: string;
      cookie_category: string;
      has_consent: boolean;
      secure_flag: boolean;
      httponly_flag: boolean;
      samesite_attribute?: string;
      expiry_date?: Date;
      purpose?: string;
      third_party: boolean;
      created_at: Date;
    }>
  > {
    try {
      return await db('gdpr_cookie_analysis').where('scan_id', scanId).orderBy('created_at', 'asc');
    } catch (error) {
      console.error('❌ Failed to retrieve cookie analysis:', error);
      throw new Error(
        `Cookie analysis retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get consent analysis for a scan
   */
  static async getConsentAnalysis(scanId: string): Promise<
    Array<{
      id: string;
      scan_id: string;
      consent_type: string;
      consent_mechanism: string;
      has_reject_option: boolean;
      has_granular_options: boolean;
      pre_ticked_boxes: boolean;
      consent_text: string;
      privacy_policy_linked: boolean;
      compliant: boolean;
      issues: string[];
      created_at: Date;
    }>
  > {
    try {
      return await db('gdpr_consent_analysis')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc');
    } catch (error) {
      console.error('❌ Failed to retrieve consent analysis:', error);
      throw new Error(
        `Consent analysis retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get tracker analysis for a scan
   */
  static async getTrackerAnalysis(scanId: string): Promise<
    Array<{
      id: string;
      scan_id: string;
      tracker_domain: string;
      tracker_type: string;
      tracker_name: string;
      loads_before_consent: boolean;
      has_consent_mechanism: boolean;
      data_transferred: string;
      privacy_policy_mentioned: boolean;
      compliant: boolean;
      created_at: Date;
    }>
  > {
    try {
      return await db('gdpr_tracker_analysis')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc');
    } catch (error) {
      console.error('❌ Failed to retrieve tracker analysis:', error);
      throw new Error(
        `Tracker analysis retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Delete scan and all related data
   */
  static async deleteScan(scanId: string): Promise<void> {
    try {
      await db.transaction(async (trx) => {
        // Delete related data first (foreign key constraints)
        await trx('gdpr_tracker_analysis').where('scan_id', scanId).del();
        await trx('gdpr_consent_analysis').where('scan_id', scanId).del();
        await trx('gdpr_cookie_analysis').where('scan_id', scanId).del();
        await trx('gdpr_check_results').where('scan_id', scanId).del();

        // Delete main scan record
        await trx('gdpr_scans').where('id', scanId).del();
      });
      console.log(`✅ Successfully deleted GDPR scan and all related data: ${scanId}`);
    } catch (error) {
      console.error('❌ Failed to delete GDPR scan:', error);
      throw new Error(
        `Scan deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }
}
